const { sql, queryWithParams } = require('../service/index.js')

class VerifyPipeController {
  /**
   * 查询指定 SystemId 的 VerifyPipe 数据
   * @param {import('koa').Context} ctx
   * @param {Function} next
   * @returns {Promise<void>}
   */
  async gain(ctx, next) {
    try {
      const { SystemId } = ctx.query
      const { recordsets } = await queryWithParams('SELECT * FROM dbo.[VerifyPipe] WHERE SystemId = @SystemId', { SystemId: { type: sql.VarChar, value: SystemId } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }

  /**
   * 更新 VerifyPipe 的 NowMaterial 和 SceneImg
   * @param {import('koa').Context} ctx
   * @param {Function} next
   * @returns {Promise<void>}
   */
  async update(ctx, next) {
    try {
      const { SystemId, NowMaterial, SceneImg } = ctx.request.body
      await queryWithParams('UPDATE dbo.[VerifyPipe] SET NowMaterial = @NowMaterial, UpdatePerson = @UpdatePerson, SceneImg = @SceneImg WHERE SystemId = @SystemId', {
        SystemId: { type: sql.VarChar, value: SystemId },
        NowMaterial: { type: sql.VarChar, value: NowMaterial },
        UpdatePerson: { type: sql.VarChar, value: ctx.user.Name },
        SceneImg: { type: sql.VarChar, value: SceneImg }
      })
      ctx.body = { code: 200, msg: '更新成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败', data: error.message }
    }
  }

  /**
   * 根据 SystemId 列表批量查询 VerifyPipe（NowMaterial 和 SceneImg 都不为 null 或 ''）
   * @param {import('koa').Context} ctx
   * @param {Function} next
   * @returns {Promise<void>}
   */
  async distinguish(ctx, next) {
    try {
      const { list } = ctx.request.body
      if (!Array.isArray(list) || list.length === 0) {
        ctx.body = { code: 200, msg: '查询成功', data: [] }
        return
      }
      // 构造参数化 IN 查询
      const params = {}
      const inClause = list
        .map((item, idx) => {
          params[`id${idx}`] = { type: sql.VarChar, value: item }
          return `@id${idx}`
        })
        .join(',')
      const queryStr = `SELECT * FROM dbo.[VerifyPipe] WHERE SystemId IN (${inClause}) AND (NowMaterial IS NOT NULL AND NowMaterial <> '') AND (SceneImg IS NOT NULL AND SceneImg <> '')`
      const { recordsets } = await queryWithParams(queryStr, params)
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }

  /**
   * 分页查询 VerifyPipe 列表
   * @param {import('koa').Context} ctx
   * @param {Function} next
   * @returns {Promise<void>}
   */
  async list(ctx, next) {
    try {
      const { page = 1, pageSize = 10 } = ctx.request.body || {}
      const pageNum = Number(page) > 0 ? Number(page) : 1
      const sizeNum = Number(pageSize) > 0 ? Number(pageSize) : 10
      // 查询总数
      const { recordsets: countSets } = await queryWithParams('SELECT COUNT(*) as total FROM dbo.[VerifyPipe]')
      const total = countSets[0][0].total
      // 查询分页数据
      const offset = (pageNum - 1) * sizeNum
      const { recordsets } = await queryWithParams('SELECT * FROM dbo.[VerifyPipe] ORDER BY SystemId OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY', {
        offset: { type: sql.Int, value: offset },
        sizeNum: { type: sql.Int, value: sizeNum }
      })
      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: { total, list: recordsets[0], page: pageNum, pageSize: sizeNum }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }

  /**
   * 对 SystemId 进行模糊搜索并分页
   * @param {import('koa').Context} ctx
   * @param {Function} next
   * @returns {Promise<void>}
   */
  async search(ctx, next) {
    try {
      const { systemId = '', page = 1, pageSize = 10 } = ctx.request.body || {}
      const pageNum = Number(page) > 0 ? Number(page) : 1
      const sizeNum = Number(pageSize) > 0 ? Number(pageSize) : 10
      let where = ''
      const params = { offset: { type: sql.Int, value: (pageNum - 1) * sizeNum }, sizeNum: { type: sql.Int, value: sizeNum } }
      if (systemId) {
        where = 'WHERE SystemId LIKE @systemId'
        params.systemId = { type: sql.VarChar, value: `%${systemId}%` }
      }
      // 查询总数
      const countSql = `SELECT COUNT(*) as total FROM dbo.[VerifyPipe] ${where}`
      const { recordsets: countSets } = await queryWithParams(countSql, params)
      const total = countSets[0][0].total
      // 查询分页数据
      const dataSql = `SELECT * FROM dbo.[VerifyPipe] ${where} ORDER BY SystemId OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`
      const { recordsets } = await queryWithParams(dataSql, params)
      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: {
          total,
          list: recordsets[0],
          page: pageNum,
          pageSize: sizeNum
        }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }

  /**
   * 按 Station 字段分类统计总量与完成数
   * @param {import('koa').Context} ctx - Koa 上下文对象
   * @param {Function} next - Koa next
   * @returns {Promise<void>}
   */
  async statistics(ctx, next) {
    try {
      // 查询每个 Station 的总量
      const { recordsets: totalSets } = await queryWithParams('SELECT Station, COUNT(*) as total FROM dbo.[VerifyPipe] GROUP BY Station')
      // 查询每个 Station 的完成数
      const { recordsets: accomplishSets } = await queryWithParams(
        "SELECT Station, COUNT(*) as accomplish FROM dbo.[VerifyPipe] WHERE (NowMaterial IS NOT NULL AND NowMaterial <> '') AND (SceneImg IS NOT NULL AND SceneImg <> '') GROUP BY Station"
      )
      const accomplishMap = new Map()
      for (const row of accomplishSets[0]) {
        accomplishMap.set(row.Station, row.accomplish)
      }
      const result = totalSets[0].map((row) => ({
        Station: row.Station,
        total: row.total,
        accomplish: accomplishMap.get(row.Station) || 0
      }))
      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: result
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }
}

module.exports = new VerifyPipeController()
