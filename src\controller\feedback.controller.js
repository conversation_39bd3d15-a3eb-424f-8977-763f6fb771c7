const { sql, query, queryWithParams } = require('../service/index.js')
const Voucher = require('../utils/wx_mp_voucher.js')

class Feedbackcontroller {
  async create(ctx, next) {
    try {
      const { FeedbackType, FeedbackContent, FeedbackImg = '' } = ctx.request.body
      if (!FeedbackType || !FeedbackContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `INSERT INTO dbo.[Feedback] 
     (FeedbackType,FeedbackContent,FeedbackUserID,LastModifyUserID,FeedbackImg) 
     values (@FeedbackType, @FeedbackContent, @FeedbackUserID, @LastModifyUserID, @FeedbackImg)`
      await queryWithParams(sentence, {
        FeedbackType: { type: sql.VarChar, value: FeedbackType },
        FeedbackContent: { type: sql.Var<PERSON>har, value: FeedbackContent },
        FeedbackUserID: { type: sql.Int, value: ctx.user.id },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        FeedbackImg: { type: sql.VarChar, value: FeedbackImg }
      })
      ctx.body = { code: 200, msg: '创建成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }

  // 创建小区档案反馈记录
  async createRecord(ctx, next) {
    try {
      const { FeedbackType = '4', FeedbackContent, FeedbackImg = '', FileCode } = ctx.request.body
      if (!FileCode) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentenceQuery = `SELECT 
    FeedbackID,
    FeedbackType,
    FeedbackContent,
    FeedbackImg,
    LastModifyUserID,
    CreateTime,
    ModifyTime,
    createUserInfo,
    newestReply,
    CASE WHEN EXISTS (
        SELECT 1 
        FROM dbo.FeedbackReplyRead r
        WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
          AND r.UserID = @UserID
    ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
FROM (
    SELECT
        f.FeedbackID,
        f.FeedbackType,
        f.FeedbackContent,
        f.FeedbackImg,
        f.LastModifyUserID,
        f.CreateTime,
        f.ModifyTime,
        (
            SELECT TOP 1 
                id, Name, Username, Station, Path 
            FROM dbo.[User] 
            WHERE id = f.FeedbackUserID 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS createUserInfo,
        (
            SELECT TOP 1 * 
            FROM dbo.[FeedbackReply] 
            WHERE FeedbackID = f.FeedbackID 
            ORDER BY CreateTime DESC 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS newestReply
    FROM dbo.[Feedback] f 
WHERE FileCode = @FileCode
) AS sub  
ORDER BY FeedbackID DESC;`
      const { recordsets } = await queryWithParams(sentenceQuery, { UserID: { type: sql.Int, value: ctx.user.id }, FileCode: { type: sql.VarChar, value: FileCode } })
      if (!!recordsets[0][0]) {
        ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
      } else {
        if (!FileCode || !FeedbackContent) return (ctx.body = { code: 400, msg: '参数缺失' })
        const sentence = `INSERT INTO dbo.[Feedback] 
       (FeedbackType,FeedbackContent,FeedbackUserID,LastModifyUserID,FeedbackImg,hide,FileCode) 
       values (@FeedbackType, @FeedbackContent, @FeedbackUserID, @LastModifyUserID, @FeedbackImg, @hide, @FileCode)`
        await queryWithParams(sentence, {
          FeedbackType: { type: sql.VarChar, value: FeedbackType },
          FeedbackContent: { type: sql.VarChar, value: FeedbackContent },
          FeedbackUserID: { type: sql.Int, value: ctx.user.id },
          LastModifyUserID: { type: sql.Int, value: ctx.user.id },
          FeedbackImg: { type: sql.VarChar, value: FeedbackImg },
          FileCode: { type: sql.VarChar, value: FileCode },
          hide: { type: sql.Int, value: 0 }
        })
        const { recordsets } = await queryWithParams(sentenceQuery, { UserID: { type: sql.Int, value: ctx.user.id }, FileCode: { type: sql.VarChar, value: FileCode } })

        ctx.body = { code: 200, msg: '创建成功', data: recordsets[0][0] }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }
  async delete(ctx, next) {
    try {
      const { id } = ctx.params
      if (!id) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `DELETE FROM dbo.[Feedback] WHERE FeedbackID = @FeedbackID`
      await queryWithParams(sentence, { FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败', error: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { FeedbackID, FeedbackType, FeedbackContent, FeedbackImg = '' } = ctx.request.body
      if (!FeedbackID || !FeedbackType || !FeedbackContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `UPDATE dbo.[Feedback] SET 
          FeedbackType = @FeedbackType,
          FeedbackContent = @FeedbackContent,
          LastModifyUserID = @LastModifyUserID,
          FeedbackImg = @FeedbackImg
          WHERE
          FeedbackID = @FeedbackID`

      await queryWithParams(sentence, {
        FeedbackType: { type: sql.VarChar, value: FeedbackType },
        FeedbackContent: { type: sql.VarChar, value: FeedbackContent },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        FeedbackImg: { type: sql.VarChar, value: FeedbackImg },
        FeedbackID: { type: sql.Int, value: FeedbackID }
      })

      const sentence2 = `SELECT 
      FeedbackID,   
      FeedbackType,
      FeedbackContent,
      FeedbackImg,   
      LastModifyUserID,
      CreateTime,   
      ModifyTime,  
      ( SELECT id , Name,Username,Station,Path 
       FROM 
       dbo.[User] 
       WHERE 
       id = LastModifyUserID FOR JSON AUTO ) AS 'createUserInfo'  
       FROM dbo.[Feedback] 
       WHERE 
       FeedbackID = @FeedbackID`
      const { recordsets } = await queryWithParams(sentence2, { FeedbackID: { type: sql.Int, value: FeedbackID } })
      ctx.body = { code: 200, msg: '更新成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败', error: error.message }
    }
  }
  async list(ctx, next) {
    try {
      const sentence = `SELECT 
          FeedbackID,
          FeedbackType,
          FeedbackContent,
          FeedbackImg,
          LastModifyUserID,
          CreateTime,
          ModifyTime,
          createUserInfo,
          newestReply,
          CASE WHEN EXISTS (
              SELECT 1 
              FROM dbo.FeedbackReplyRead r
              WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
                AND r.UserID = @UserID
          ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
      FROM (
          SELECT
              f.FeedbackID,
              f.FeedbackType,
              f.FeedbackContent,
              f.FeedbackImg,
              f.LastModifyUserID,
              f.CreateTime,
              f.ModifyTime,
              (
                  SELECT TOP 1 
                      id, Name, Username, Station, Path 
                  FROM dbo.[User] 
                  WHERE id = f.FeedbackUserID 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS createUserInfo,
              (
                  SELECT TOP 1 * 
                  FROM dbo.[FeedbackReply] 
                  WHERE FeedbackID = f.FeedbackID 
                  ORDER BY CreateTime DESC 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS newestReply
          FROM dbo.[Feedback] f
      WHERE hide = 1
      ) AS sub  
      ORDER BY FeedbackID DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  // 小区档案错误反馈列表
  async listRecord(ctx, next) {
    const { low = 'false', page = 1, pageSize = 15, name, start, end } = ctx.request.query
    try {
      // 转换分页参数为数字
      const pageNum = parseInt(page)
      const pageSizeNum = parseInt(pageSize)
      const offset = (pageNum - 1) * pageSizeNum

      // 处理日期参数 - 确保日期范围包含完整的时间
      // 数据库中存储的是UTC时间格式(如: 2025-07-07T16:13:33.950Z)，需要使用UTC格式进行比较
      let processedStart = start
      let processedEnd = end
      if (start && end) {
        // 如果start日期没有时间部分，添加T00:00:00.000Z确保从当天开始（UTC时间）
        if (start && !start.includes(':')) {
          processedStart = start + 'T00:00:00.000Z'
        }
        // 如果end日期没有时间部分，添加T23:59:59.999Z确保包含当天所有数据（UTC时间）
        if (end && !end.includes(':')) {
          processedEnd = end + 'T23:59:59.999Z'
        }
      }

      // 获取总数的查询 - 只统计有回复记录的反馈
      const countSentence = `SELECT COUNT(*) as total
      FROM dbo.[Feedback] f
      WHERE FileCode IS NOT NULL AND FileCode <> ''
        AND EXISTS (
            SELECT 1
            FROM dbo.[FeedbackReply] r
            WHERE r.FeedbackID = f.FeedbackID
            ${start && end ? 'AND r.ModifyTime >= @start AND r.ModifyTime <= @end' : ''}
        )
        ${name ? 'AND FeedbackContent LIKE @name' : ''}`

      const countParams = {}
      if (name) {
        countParams.name = { type: sql.VarChar, value: `%${name}%` }
      }
      if (start && end) {
        countParams.start = { type: sql.DateTime, value: processedStart }
        countParams.end = { type: sql.DateTime, value: processedEnd }
      }
      const { recordsets: countResult } = await queryWithParams(countSentence, countParams)
      const total = countResult[0][0].total

      // 分页查询主数据
      const sentence = `SELECT
          FeedbackID,
          FileCode,
          FeedbackType,
          FeedbackContent,
          FeedbackImg,
          LastModifyUserID,
          CreateTime,
          ModifyTime,
          createUserInfo,
          newestReply,
          pendingCount,
          processedCount,
          CASE WHEN EXISTS (
              SELECT 1
              FROM dbo.FeedbackReplyRead r
              WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')
                AND r.UserID = @UserID
          ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
      FROM (
          SELECT
              f.FeedbackID,
              f.FileCode,
              f.FeedbackType,
              f.FeedbackContent,
              f.FeedbackImg,
              f.LastModifyUserID,
              f.CreateTime,
              f.ModifyTime,
              (
                  SELECT TOP 1
                      id, Name, Username, Station, Path
                  FROM dbo.[User]
                  WHERE id = f.FeedbackUserID
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
              ) AS createUserInfo,
              (
                  SELECT TOP 1 *
                  FROM dbo.[FeedbackReply]
                  WHERE FeedbackID = f.FeedbackID
                  ORDER BY CreateTime DESC
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
              ) AS newestReply,
              (
                  SELECT COUNT(*)
                  FROM dbo.FeedbackReply r
                  WHERE r.FeedbackID = f.FeedbackID
                    AND r.task = 1
                    AND r.perform = 0
              ) AS pendingCount,
              (
                  SELECT COUNT(*)
                  FROM dbo.FeedbackReply r
                  WHERE r.FeedbackID = f.FeedbackID
                    AND r.task = 1
                    AND r.perform = 1
              ) AS processedCount
          FROM dbo.[Feedback] f
          WHERE FileCode IS NOT NULL AND FileCode <> ''
            AND EXISTS (
                SELECT 1
                FROM dbo.[FeedbackReply] r
                WHERE r.FeedbackID = f.FeedbackID
                ${start && end ? 'AND r.ModifyTime >= @start AND r.ModifyTime <= @end' : ''}
            )
            ${name ? 'AND FeedbackContent LIKE @name' : ''}
      ) AS sub
      ORDER BY
          pendingCount DESC,
          JSON_VALUE(newestReply, '$.ModifyTime') DESC,
          processedCount DESC
      OFFSET @offset ROWS
      FETCH NEXT @pageSize ROWS ONLY;`

      const queryParams = {
        UserID: { type: sql.Int, value: ctx.user.id },
        offset: { type: sql.Int, value: offset },
        pageSize: { type: sql.Int, value: pageSizeNum }
      }
      if (name) {
        queryParams.name = { type: sql.VarChar, value: `%${name}%` }
      }
      if (start && end) {
        queryParams.start = { type: sql.DateTime, value: processedStart }
        queryParams.end = { type: sql.DateTime, value: processedEnd }
      }
      const { recordsets } = await queryWithParams(sentence, queryParams)

      let data = recordsets[0]

      // 如果需要过滤待处理的记录
      if (low === 'true') {
        data = data.filter((i) => i.pendingCount > 0)
      }

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSizeNum)

      ctx.body = {
        code: 200,
        msg: '查询成功',
        data,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  // 模糊搜索小区档案错误反馈
  async search(ctx, next) {
    const { name } = ctx.query
    try {
      const sentence = `SELECT 
          FeedbackID,
          FeedbackType,
          FeedbackContent,
          FeedbackImg,
          LastModifyUserID,
          FileCode,
          CreateTime,
          ModifyTime,
          createUserInfo,
          newestReply,
          pendingCount,
          CASE WHEN EXISTS (
              SELECT 1 
              FROM dbo.FeedbackReplyRead r
              WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
                AND r.UserID = @UserID
          ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
      FROM (
          SELECT
              f.FeedbackID,
              f.FeedbackType,
              f.FeedbackContent,
              f.FeedbackImg,
              f.LastModifyUserID,
              f.FileCode,
              f.CreateTime,
              f.ModifyTime,
              (
                  SELECT TOP 1 
                      id, Name, Username, Station, Path 
                  FROM dbo.[User] 
                  WHERE id = f.FeedbackUserID 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS createUserInfo,
              (
                  SELECT TOP 1 * 
                  FROM dbo.[FeedbackReply] 
                  WHERE FeedbackID = f.FeedbackID 
                  ORDER BY CreateTime DESC 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS newestReply,
              (
                  SELECT COUNT(*)
                  FROM dbo.FeedbackReply r
                  WHERE r.FeedbackID = f.FeedbackID
                    AND r.task = 1
                    AND r.perform = 0
              ) AS pendingCount
          FROM dbo.[Feedback] f
      WHERE hide = 1 AND FileCode IS NOT NULL AND FileCode <> '' AND FeedbackContent LIKE '%${name}%'
      ) AS sub  
      ORDER BY pendingCount DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async hide(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `UPDATE dbo.[Feedback] SET hide = 0 WHERE FeedbackID = @FeedbackID`
      await queryWithParams(sentence, { FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '隐藏成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '隐藏失败', error: error.message }
    }
  }
  async gain(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `SELECT 
    FeedbackID,
    FeedbackType,
    FeedbackContent,
    FeedbackImg,
    LastModifyUserID,
    CreateTime,
    ModifyTime,
    createUserInfo,
    newestReply,
    FileCode,
    CASE WHEN EXISTS (
        SELECT 1 
        FROM dbo.FeedbackReplyRead r
        WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
          AND r.UserID = @UserID
    ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
FROM (
    SELECT
        f.FeedbackID,
        f.FeedbackType,
        f.FeedbackContent,
        f.FeedbackImg,
        f.LastModifyUserID,
        f.CreateTime,
        f.ModifyTime,
        f.FileCode,
        (
            SELECT TOP 1 
                id, Name, Username, Station, Path 
            FROM dbo.[User] 
            WHERE id = f.FeedbackUserID 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS createUserInfo,
        (
            SELECT TOP 1 * 
            FROM dbo.[FeedbackReply] 
            WHERE FeedbackID = f.FeedbackID 
            ORDER BY CreateTime DESC 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS newestReply
    FROM dbo.[Feedback] f
WHERE FeedbackID = @FeedbackID
) AS sub  
ORDER BY FeedbackID DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id }, FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      console.log(error)
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

class Replycontroller {
  async create(ctx, next) {
    try {
      const { FeedbackID, ReplyContent, ReplyImg = '', task = 0, perform = 0, FeedbackContent, FileCode } = ctx.request.body
      if (!ReplyContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `INSERT INTO dbo.[FeedbackReply] 
     (FeedbackID, ReplyContent, ReplyUserID, LastModifyUserID, ReplyImg, task, perform) 
     values 
     (@FeedbackID, @ReplyContent, @ReplyUserID, @LastModifyUserID, @ReplyImg, @task, @perform)`

      await queryWithParams(sentence, {
        FeedbackID: { type: sql.Int, value: FeedbackID },
        ReplyContent: { type: sql.VarChar, value: ReplyContent },
        ReplyUserID: { type: sql.Int, value: ctx.user.id },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        task: { type: sql.Int, value: task },
        perform: { type: sql.Int, value: perform },
        ReplyImg: { type: sql.VarChar, value: ReplyImg }
      })

      if (FileCode) {
        const content = ReplyContent.length > 20 ? ReplyContent.slice(0, 17) + '...' : ReplyContent
        const found = { thing25: { value: ctx.user.Name }, thing21: { value: FeedbackContent }, thing20: { value: content } }
        Voucher.sendTemplateMessage('QXW_1EfJgf-HwdbRsGOhJW9eU_-BgkA76ivjU2IwACA', found, FileCode) //创建通知
      }
      ctx.body = { code: 200, msg: '发送成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '发送失败', error: error.message }
    }
  }
  async delete(ctx, next) {
    try {
      const { id } = ctx.params
      if (!id) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `DELETE FROM dbo.[FeedbackReply] WHERE ReplyID = @ReplyID`
      await queryWithParams(sentence, { ReplyID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败', error: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { ReplyID, ReplyContent, ReplyImg = '', perform, remark = '', FeedbackContent, FileCode } = ctx.request.body
      if (!ReplyID || !ReplyContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `UPDATE dbo.[FeedbackReply] SET 
      ReplyContent = @ReplyContent,
      LastModifyUserID = @LastModifyUserID,
      perform = @perform,
      ReplyImg = @ReplyImg,
      remark = @remark
      WHERE
      ReplyID = @ReplyID`

      await queryWithParams(sentence, {
        ReplyContent: { type: sql.VarChar, value: ReplyContent },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        ReplyImg: { type: sql.VarChar, value: ReplyImg },
        ReplyID: { type: sql.Int, value: ReplyID },
        perform: { type: sql.Int, value: perform },
        remark: { type: sql.VarChar, value: remark }
      })

      const sentence2 = `SELECT *, 
      ( SELECT id , Name,Username,Station,Path FROM dbo.[User] 
      WHERE id = LastModifyUserID FOR JSON AUTO ) AS 'createUserInfo'  
      FROM dbo.[FeedbackReply] 
      WHERE ReplyID = @ReplyID`

      const { recordsets } = await queryWithParams(sentence2, { ReplyID: { type: sql.Int, value: ReplyID } })

      if (FileCode) {
        const release = { thing4: { value: ctx.user.Name }, thing25: { value: FeedbackContent }, phrase15: { value: '处理完成' } }
        Voucher.sendTemplateMessage('sC3l9PgLnuUnceswsvKUc3iTUJkKbTWFwf1jaNxoN6c', release, FileCode) //处理通知
      }

      ctx.body = { code: 200, msg: '更新成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败', error: error.message }
    }
  }
  async list(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `SELECT *,
    ( SELECT id , Name,Username,Station,Path FROM dbo.[User] WHERE id = ReplyUserID FOR JSON AUTO ) AS 'createUserInfo',  
    ( SELECT id , Name,Username,Station,Path FROM dbo.[User] WHERE id = LastModifyUserID FOR JSON AUTO ) AS 'replyUserInfo'  
FROM   
    dbo.[FeedbackReply]  
WHERE   
    hide = 1 AND FeedbackID = @FeedbackID
ORDER BY   
    ReplyID DESC;`
      const { recordsets } = await queryWithParams(sentence, { FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }

  // 查询回复列表内需要处理的问题点数量
  async pendingCount(ctx, next) {
    try {
      const sentence = `SELECT COUNT(*) AS 'count' FROM dbo.[FeedbackReply] WHERE task = 1 AND perform = 0`
      const { recordsets } = await queryWithParams(sentence, {})
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      this.ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async hide(ctx, next) {
    const release = { thing4: { value: '发布人' }, thing25: { value: '发布位置' }, thing15: { value: '问题描述' } }
    const res = await Voucher.sendTemplateMessage('QXW_1EfJgf-HwdbRsGOhJW9eU_-BgkA76ivjU2IwACA', found, 'FT0015') //创建通知
    // Voucher.sendTemplateMessage('sC3l9PgLnuUnceswsvKUc3iTUJkKbTWFwf1jaNxoN6c', release) //处理通知
    ctx.body = { code: 200, msg: '成功' }

    // const { id } = ctx.params
    // try {
    //   const sentence = `UPDATE dbo.[FeedbackReply] SET hide = 0 WHERE ReplyID = @ReplyID`
    //   await queryWithParams(sentence, { ReplyID: { type: sql.Int, value: id } })
    //   ctx.body = { code: 200, msg: '删除成功' }
    // } catch (error) {
    //   ctx.body = { code: 500, msg: '删除失败', error: error.message }
    // }
  }
}

class ReplyReadcontroller {
  async create(ctx, next) {
    try {
      const { ReplyID } = ctx.request.body
      if (!ReplyID) return (ctx.body = { code: 400, msg: '参数错误' })
      const verifyParameter = { UserID: { type: sql.Int, value: ctx.user.id }, ReplyID: { type: sql.Int, value: ReplyID } }
      const sentenceQuery = `SELECT * FROM dbo.[FeedbackReplyRead] WHERE ReplyID = @ReplyID AND UserID = @UserID`
      const { recordsets } = await queryWithParams(sentenceQuery, verifyParameter)
      if (recordsets[0].length) return (ctx.body = { code: 200, msg: '已存在' })
      const sentence = `INSERT INTO dbo.[FeedbackReplyRead] (UserID, ReplyID) VALUES (@UserID, @ReplyID)`
      await queryWithParams(sentence, verifyParameter)
      ctx.body = { code: 200, msg: '创建成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }
  async gain(ctx, next) {
    const { ReplyID } = ctx.params
    try {
      const sentence = `SELECT * FROM dbo.[FeedbackReplyRead] WHERE ReplyID = @ReplyID AND UserID = @UserID`
      const { recordsets } = await queryWithParams(sentence, {
        ReplyID: { type: sql.Int, value: ReplyID },
        UserID: { type: sql.Int, value: ctx.user.id }
      })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async list(ctx, next) {
    try {
      const { ReplyID } = ctx.params
      const sentence = `SELECT u.id , u.Username ,u.Name,u.Station,u.path FROM dbo.FeedbackReplyRead frr INNER JOIN dbo.[User] u ON frr.UserID = u.id WHERE
    frr.ReplyID = @ReplyID ORDER BY u.id DESC`
      const { recordsets } = await queryWithParams(sentence, {
        ReplyID: { type: sql.Int, value: ReplyID }
      })

      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

const feedback = new Feedbackcontroller()
const reply = new Replycontroller()
const read = new ReplyReadcontroller()
module.exports = { feedback, reply, read }
