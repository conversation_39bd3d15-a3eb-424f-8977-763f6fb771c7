const { koaBody } = require('koa-body') //参数解析
const mime = require('mime-types')
const eviltransform = require('eviltransform')
const { join } = require('path')
const fs = require('fs')

const OSS = require('ali-oss')
const { STS } = require('ali-oss')
const { getCredential } = require('ali-oss/lib/common/signUtils')
const { getStandardRegion } = require('ali-oss/lib/common/utils/getStandardRegion')
const { policy2Str } = require('ali-oss/lib/common/utils/policy2Str')

class ResourceManagementController {
  // 解析pdf word 文件
  analysisDispose() {}
  //   坐标转换
  coordinateTransformation(ctx, next) {
    try {
      const { lat, lng, fn = 'wgs2gcj' } = ctx.request.body
      if (!lat || !lng) return (ctx.body = { code: 400, message: '缺少参数' })
      const latLng = eviltransform[fn](lat, lng)
      ctx.body = { code: 200, data: latLng }
    } catch (error) {
      ctx.body = { code: 500, message: error.message }
    }
  }

  // 导出外业数据
  exportFieldWork() {}

  //   导出gis维护
  exportPreserve() {}

  //   导出小额项目
  exportProject() {}

  //  上传文件
  async uploadFile(ctx, next) {
    try {
      const { baseUrl } = ctx.params //上传的基础地址
      const { url = '' } = ctx.query
      const basePath = join(__dirname, `../../updates/${baseUrl}`)
      if (!fs.existsSync(basePath)) return (ctx.body = { code: 404, message: '路径不存在', data: null })
      const filePath = join(basePath, url)
      if (!fs.existsSync(filePath)) fs.mkdirSync(filePath, { recursive: true })
      const options = { multipart: true, formidable: { uploadDir: filePath, keepExtensions: true } }
      await koaBody(options)(ctx, next)
    } catch (error) {
      ctx.body = { code: 400, message: '文件上传失败', data: null }
    }
  }

  async uploadFileSucceed(ctx, next) {
    const { baseUrl } = ctx.params //上传的基础地址
    const { url = '' } = ctx.query
    const { newFilename } = ctx.request.files.file
    const filePath = join(`/${baseUrl}/${url}`, newFilename).replace(/\\/g, '/')
    ctx.body = { code: 200, message: '上传成功', data: `https://www.szwgft.cn/nodeServer${filePath}`, ff: ctx.request.files.file }
  }

  async readFileList(ctx, next) {
    const { baseUrl } = ctx.params
    const { url = '' } = ctx.query
    try {
      const basePath = join(__dirname, `../../updates/${baseUrl}`)
      const dirPath = join(basePath, url)
      if (!fs.existsSync(dirPath)) return (ctx.body = { code: 404, message: '路径不存在', data: null })

      const files = fs.readdirSync(dirPath).filter((i) => !fs.statSync(join(dirPath, i)).isDirectory())
      const data = files.map((file) => {
        const size = (fs.statSync(dirPath + `/${file}`).size / 1024 / 1024).toFixed(2)
        const path = `https://www.szwgft.cn//nodeServer/${baseUrl}/${join(url, encodeURIComponent(file))}`.replace(/\\/g, '/')
        const type = mime.lookup(dirPath + `/${file}`)
        return { file, path, size, type }
      })

      ctx.body = { code: 200, message: '查询成功', data }
    } catch (error) {
      ctx.body = { code: 500, message: '查询失败', data: error.message }
    }
  }
  // 发放OSS STS Token
  // async ossVoucher(ctx, next) {
  //   try {
  //     // 从环境变量获取OSS配置
  //     const { OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, OSS_BUCKET, OSS_ROLE_ARN, OSS_REGION } = process.env
  //     const filePath = join(__dirname, '../../updates/img/1.2.14.apk')
  //     const client = new OSS({ authorizationV4: true, region: OSS_REGION, bucket: OSS_BUCKET, accessKeyId: OSS_ACCESS_KEY_ID, accessKeySecret: OSS_ACCESS_KEY_SECRET, secure: true })
  //     const headers = {
  //       // 指定Object的存储类型。
  //       'x-oss-storage-class': 'Standard',
  //       // 指定Object的访问权限。
  //       'x-oss-object-acl': 'private',
  //       // 'x-oss-object-acl': 'public-read',
  //       // 通过文件URL访问文件时，指定以附件形式下载文件，下载后的文件名称定义为example.txt。
  //       // 'Content-Disposition': 'attachment; filename="example.txt"',
  //       // 设置Object的标签，可同时设置多个标签。
  //       'x-oss-tagging': 'Tag1=1&Tag2=2',
  //       // 指定PutObject操作时是否覆盖同名目标Object。此处设置为true，表示禁止覆盖同名Object。
  //       'x-oss-forbid-overwrite': 'false'
  //     }
  //     const result = await client.put('1.2.14.apk', filePath, { headers })
  //     ctx.body = result
  //   } catch (error) {
  //     console.error('OSS错误:', error)
  //     ctx.body = {
  //       code: 500,
  //       message: '获取STS Token失败',
  //       data: error.message
  //     }
  //   }
  // }

  async ossVoucher(ctx, next) {
    const { type = 'file' } = ctx.query
    const bucket = type === 'img' ? 'ft-oss-image' : 'ft-oss-data'
    try {
      // 初始化STS客户端
      let sts = new STS({ accessKeyId: process.env.OSS_ACCESS_KEY_ID, accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET })

      // 调用assumeRole接口获取STS临时访问凭证
      const result = await sts.assumeRole(process.env.OSS_ROLE_ARN, '', '3600', 'futRam')

      // 提取临时访问凭证中的AccessKeyId、AccessKeySecret和SecurityToken
      const accessKeyId = result.credentials.AccessKeyId
      const accessKeySecret = result.credentials.AccessKeySecret
      const securityToken = result.credentials.SecurityToken

      const client = new OSS({
        bucket, // 请替换为目标Bucket名称
        region: process.env.OSS_REGION, // 请替换为标Bucket所在地域
        accessKeyId,
        accessKeySecret,
        stsToken: securityToken,
        refreshSTSTokenInterval: 0,
        refreshSTSToken: async () => {
          const { accessKeyId, accessKeySecret, securityToken } = await client.getCredential()
          return { accessKeyId, accessKeySecret, stsToken: securityToken }
        }
      })

      // 创建表单数据Map
      const formData = new Map()

      // 设置签名过期时间为当前时间往后推10分钟
      const date = new Date()
      const expirationDate = new Date(date)
      expirationDate.setMinutes(date.getMinutes() + 10)

      // 格式化日期为符合ISO 8601标准的UTC时间字符串格式
      function padTo2Digits(num) {
        return num.toString().padStart(2, '0')
      }
      function formatDateToUTC(date) {
        return date.getUTCFullYear() + padTo2Digits(date.getUTCMonth() + 1) + padTo2Digits(date.getUTCDate()) + 'T' + padTo2Digits(date.getUTCHours()) + padTo2Digits(date.getUTCMinutes()) + padTo2Digits(date.getUTCSeconds()) + 'Z'
      }
      const formattedDate = formatDateToUTC(expirationDate)

      // 生成x-oss-credential并设置表单数据
      const credential = getCredential(formattedDate.split('T')[0], getStandardRegion(client.options.region), client.options.accessKeyId)
      formData.set('x_oss_date', formattedDate)
      formData.set('x_oss_credential', credential)
      formData.set('x_oss_signature_version', 'OSS4-HMAC-SHA256')

      // 创建policy
      // 示例policy表单域只列举必填字段
      const policy = {
        expiration: expirationDate.toISOString(),
        conditions: [{ bucket }, { 'x-oss-credential': credential }, { 'x-oss-signature-version': 'OSS4-HMAC-SHA256' }, { 'x-oss-date': formattedDate }]
      }

      // 如果存在STS Token，添加到策略和表单数据中
      if (client.options.stsToken) {
        policy.conditions.push({ 'x-oss-security-token': client.options.stsToken })
        formData.set('security_token', client.options.stsToken)
      }

      // 生成签名并设置表单数据
      const signature = client.signPostObjectPolicyV4(policy, date)
      formData.set('policy', Buffer.from(policy2Str(policy), 'utf8').toString('base64'))
      formData.set('signature', signature)

      ctx.body = {
        code: 200,
        message: '获取STS Token成功',
        data: {
          host: `http://${client.options.bucket}.${client.options.region}.aliyuncs.com`,
          policy: Buffer.from(policy2Str(policy), 'utf8').toString('base64'),
          x_oss_signature_version: 'OSS4-HMAC-SHA256',
          x_oss_credential: credential,
          x_oss_date: formattedDate,
          signature: signature,
          dir: 'oss/', // 指定上传到OSS的文件前缀
          security_token: client.options.stsToken
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取STS Token失败',
        data: error.message
      }
    }
  }
}
// 统计 添加两个字段 添加图层

module.exports = new ResourceManagementController()
