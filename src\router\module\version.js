const Router = require('@koa/router')
const { createVersion, deleteVersion, updateVersion, compareVersion, getVersionsList } = require('../../controller/version.controller.js')
const router = new Router({ prefix: '/version' })

router.post('/create', createVersion) //版本创建
router.delete('/delete', deleteVersion) //版本删除
router.put('/update', updateVersion) //版本更新
router.get('/list', getVersionsList) //版本列表
router.get('/compare', compareVersion) //更新检查

module.exports = router
