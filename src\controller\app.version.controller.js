const fs = require('fs')
const path = require('path')

const { compare } = require('../utils/index')

const dirPath = path.resolve(__dirname, '../assets/data/UpdateConfig/index.json')
// 获取版本列表
async function getVersionsList(ctx) {
  try {
    const fileContent = await fs.promises.readFile(dirPath, 'utf8')
    const data = JSON.parse(fileContent)
    ctx.body = { code: 200, data: data.versions }
  } catch (error) {
    ctx.body = { code: 500, message: error.message }
  }
}

// 版本更新检查
async function compareVersion(ctx) {
  try {
    const { version } = ctx.query
    const fileContent = await fs.promises.readFile(dirPath, 'utf8')
    const Content = JSON.parse(fileContent)
    // 查找最新可更新的版本
    const currentVersion = Content.versions.filter((i) => i.allowUptate).at(-1)
    //是否有新版本
    const isNewVersion = compare(currentVersion.version, version)
    const isAllowedTime = new Date().getTime() >= new Date(currentVersion.allowUptateTime).getTime()
    const renewable = isNewVersion && isAllowedTime
    ctx.body = { code: 200, renewable, data: currentVersion }
  } catch (error) {
    ctx.body = { code: 500, message: error.message }
  }
}

// 新增版本
async function createVersion(ctx) {
  try {
    const val = ctx.request.body
    const fileContent = await fs.promises.readFile(dirPath, 'utf8')
    const Content = JSON.parse(fileContent)
    // 查找最新可更新的版本
    const currentVersion = Content.versions.filter((i) => i.allowUptate).at(-1).version
    const isExists = Content.versions.some((i) => i.version == val.version)
    if (isExists) return (ctx.body = { code: 400, data: { currentVersion }, message: '版本号已存在' })
    if (!compare(val.version, Content.versions.at(-1).version)) return (ctx.body = { code: 500, data: { currentVersion }, message: '版本号小于最新版本号' })
    Content.versions.push(val)
    await fs.promises.writeFile(dirPath, JSON.stringify(Content))
    ctx.body = { code: 200, message: '版本创建成功', data: Content.versions }
  } catch (error) {
    ctx.body = { code: 500, message: error.message }
  }
}
// 删除版本
async function deleteVersion(ctx) {
  try {
    const val = ctx.request.query
    const fileContent = await fs.promises.readFile(dirPath, 'utf8')
    const Content = JSON.parse(fileContent)
    const isExists = Content.versions.some((i) => i.version == val.version)
    if (!isExists) return (ctx.body = { code: 400, message: '版本号不存在' })
    Content.versions = Content.versions.filter((item) => item.version != val.version)
    await fs.promises.writeFile(dirPath, JSON.stringify(Content))
    ctx.body = { code: 200, message: '版本删除成功', data: Content.versions }
  } catch (error) {
    ctx.body = { code: 500, message: error.message }
  }
}
// 更新版本
async function updateVersion(ctx) {
  try {
    const val = ctx.request.body
    const fileContent = await fs.promises.readFile(dirPath, 'utf8')
    const Content = JSON.parse(fileContent)
    const isExists = Content.versions.some((i) => i.version == val.version)
    if (!isExists) return (ctx.body = { code: 400, message: '版本号不存在' })
    Content.versions = Content.versions.map((item) => (item.version == val.version ? val : item))
    await fs.promises.writeFile(dirPath, JSON.stringify(Content))
    ctx.body = { code: 200, data: Content.versions, message: '版本更新成功' }
  } catch (error) {
    ctx.body = { code: 500, message: error.message }
  }
}

module.exports = { getVersionsList, createVersion, deleteVersion, updateVersion, compareVersion }
